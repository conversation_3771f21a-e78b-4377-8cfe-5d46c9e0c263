import { Blockchain, SandboxContract, TreasuryContract } from '@ton/sandbox';
import { toNano } from '@ton/core';
import { OnionAuction } from '../build/OnionAuction/OnionAuction_OnionAuction';
import '@ton/test-utils';

describe('OnionAuction Min Purchase Configuration', () => {
    let blockchain: Blockchain;
    let deployer: SandboxContract<TreasuryContract>;
    let onionAuction: SandboxContract<OnionAuction>;

    beforeEach(async () => {
        blockchain = await Blockchain.create();
        deployer = await blockchain.treasury('deployer');

        // Create auction with proper parameters
        const startTime = BigInt(Math.floor(Date.now() / 1000));
        const endTime = startTime + 86400n; // 24 hours later
        const softCap = toNano('500000'); // 500k TON
        const hardCap = toNano('2000000'); // 2M TON  
        const totalSupply = toNano('1000000'); // 1M tokens

        onionAuction = blockchain.openContract(
            await OnionAuction.fromInit(
                deployer.address,
                startTime,
                endTime,
                softCap,
                hardCap,
                totalSupply
            )
        );

        const deployResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.5') },
            'Deploy'
        );

        expect(deployResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            success: true,
        });

        // Start the auction
        const startTime2 = BigInt(Math.floor(Date.now() / 1000));
        const endTime2 = startTime2 + 86400n;
        
        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'StartAuction',
                start_time: startTime2,
                end_time: endTime2,
                soft_cap: toNano('500000'),
                hard_cap: toNano('2000000'),
                initial_price: toNano('0.1')
            }
        );
    });

    it('should have default minimum purchase amount', async () => {
        const minPurchase = await onionAuction.getMinPurchase();
        expect(minPurchase.toString()).toBe(toNano('0.1').toString()); // Default 0.1 TON
    });

    it('should allow owner to set minimum purchase amount', async () => {
        const newMinPurchase = toNano('0.5'); // 0.5 TON

        const setResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            {
                $$type: 'SetMinPurchase',
                min_purchase: newMinPurchase
            }
        );

        expect(setResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            success: true,
        });

        // Check that minimum purchase amount was updated
        const minPurchase = await onionAuction.getMinPurchase();
        expect(minPurchase.toString()).toBe(newMinPurchase.toString());
    });

    it('should reject purchase below minimum amount', async () => {
        // Set minimum purchase to 1 TON
        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            {
                $$type: 'SetMinPurchase',
                min_purchase: toNano('1')
            }
        );

        const buyer = await blockchain.treasury('buyer');
        
        // Try to purchase with 0.5 TON (below minimum)
        const purchaseResult = await onionAuction.send(
            buyer.getSender(),
            { value: toNano('2.5') }, // 0.5 for purchase + 2 for gas
            {
                $$type: 'Purchase',
                amount: toNano('0.5'), // Below minimum
                currency: 0n
            }
        );

        expect(purchaseResult.transactions).toHaveTransaction({
            from: buyer.address,
            to: onionAuction.address,
            success: false,
        });
    });

    it('should accept purchase above minimum amount', async () => {
        // Set minimum purchase to 0.5 TON
        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            {
                $$type: 'SetMinPurchase',
                min_purchase: toNano('0.5')
            }
        );

        const buyer = await blockchain.treasury('buyer');
        
        // Purchase with 1 TON (above minimum)
        const purchaseResult = await onionAuction.send(
            buyer.getSender(),
            { value: toNano('3') }, // 1 for purchase + 2 for gas
            {
                $$type: 'Purchase',
                amount: toNano('1'), // Above minimum
                currency: 0n
            }
        );

        expect(purchaseResult.transactions).toHaveTransaction({
            from: buyer.address,
            to: onionAuction.address,
            success: true,
        });

        // Check that total raised increased
        const totalRaised = await onionAuction.getTotalRaised();
        expect(totalRaised > 0n).toBe(true);
    });

    it('should reject non-owner attempts to set minimum purchase', async () => {
        const nonOwner = await blockchain.treasury('nonOwner');
        
        const setResult = await onionAuction.send(
            nonOwner.getSender(),
            { value: toNano('0.05') },
            {
                $$type: 'SetMinPurchase',
                min_purchase: toNano('2')
            }
        );

        expect(setResult.transactions).toHaveTransaction({
            from: nonOwner.address,
            to: onionAuction.address,
            success: false,
        });

        // Check that minimum purchase amount was not changed
        const minPurchase = await onionAuction.getMinPurchase();
        expect(minPurchase.toString()).toBe(toNano('0.1').toString()); // Should still be default
    });

    it('should reject zero or negative minimum purchase amount', async () => {
        const setResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            {
                $$type: 'SetMinPurchase',
                min_purchase: 0n // Zero amount
            }
        );

        expect(setResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            success: false,
        });

        // Check that minimum purchase amount was not changed
        const minPurchase = await onionAuction.getMinPurchase();
        expect(minPurchase.toString()).toBe(toNano('0.1').toString()); // Should still be default
    });
});
