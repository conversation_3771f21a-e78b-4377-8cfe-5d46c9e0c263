'use client'

import { useState, useCallback } from 'react'
import { useTonWallet } from '@tonconnect/ui-react'
import { ApiService, PurchaseCalculationResponse, handleApiError } from '@/lib/apiService'

export interface SignaturePurchaseState {
  isCalculating: boolean
  isProcessing: boolean
  calculation: PurchaseCalculationResponse | null
  error: string | null
}

export interface PurchaseParams {
  amount: string
  currency: 'TON' | 'USDT'
}

export function useSignaturePurchase() {
  const wallet = useTonWallet()
  const [state, setState] = useState<SignaturePurchaseState>({
    isCalculating: false,
    isProcessing: false,
    calculation: null,
    error: null
  })

  /**
   * Calculate purchase with signature verification
   * This is the only supported purchase method - all purchases must be signature verified
   */
  const calculatePurchase = useCallback(async (params: PurchaseParams) => {
    if (!wallet?.account?.address) {
      setState(prev => ({ ...prev, error: 'Wallet not connected' }))
      return null
    }

    setState(prev => ({ 
      ...prev, 
      isCalculating: true, 
      error: null,
      calculation: null 
    }))

    try {
      // Convert amount to nanotons/smallest unit
      const amountBigInt = BigInt(Math.floor(parseFloat(params.amount) * **********))
      const currency = params.currency === 'TON' ? 0 : 1

      const response = await ApiService.calculatePurchase({
        user_address: wallet.account.address,
        amount: amountBigInt.toString(),
        currency
      })

      if (!response.success) {
        throw new Error(response.error || 'Calculation failed')
      }

      setState(prev => ({ 
        ...prev, 
        isCalculating: false,
        calculation: response
      }))

      return response
    } catch (error) {
      const errorMessage = handleApiError(error)
      setState(prev => ({ 
        ...prev, 
        isCalculating: false,
        error: errorMessage
      }))
      return null
    }
  }, [wallet])

  /**
   * Execute purchase with signature verification
   * All purchases must go through this signature verification process
   */
  const executePurchase = useCallback(async () => {
    if (!wallet || !state.calculation) {
      setState(prev => ({ ...prev, error: 'No calculation available' }))
      return false
    }

    setState(prev => ({ ...prev, isProcessing: true, error: null }))

    try {
      // Here you would send the transaction to the smart contract
      // with the calculation and signature
      
      // For now, we'll simulate the transaction
      console.log('Executing purchase with signature:', {
        calculation: state.calculation.calculation,
        signature: state.calculation.signature
      })

      // Simulate processing delay
      await new Promise(resolve => setTimeout(resolve, 2000))

      // In a real implementation, you would:
      // 1. Create the PurchaseWithSignature message
      // 2. Send it to the smart contract
      // 3. Wait for confirmation
      
      setState(prev => ({ 
        ...prev, 
        isProcessing: false,
        calculation: null // Clear after successful purchase
      }))

      return true
    } catch (error) {
      const errorMessage = handleApiError(error)
      setState(prev => ({ 
        ...prev, 
        isProcessing: false,
        error: errorMessage
      }))
      return false
    }
  }, [wallet, state.calculation])

  /**
   * Clear current calculation
   */
  const clearCalculation = useCallback(() => {
    setState(prev => ({ 
      ...prev, 
      calculation: null,
      error: null
    }))
  }, [])

  /**
   * Clear error
   */
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }))
  }, [])

  /**
   * Get formatted calculation data for display
   */
  const getFormattedCalculation = useCallback(() => {
    if (!state.calculation) return null

    const calc = state.calculation.calculation
    return {
      amount: parseFloat(calc.amount) / **********, // Convert from nanotons
      currency: calc.currency === 0 ? 'TON' : 'USDT',
      tokensToReceive: parseFloat(calc.tokens_to_receive) / **********,
      currentPrice: parseFloat(calc.current_price) / **********,
      currentRound: calc.current_round,
      timestamp: new Date(calc.timestamp * 1000),
      nonce: calc.nonce
    }
  }, [state.calculation])

  return {
    ...state,
    calculatePurchase,
    executePurchase,
    clearCalculation,
    clearError,
    getFormattedCalculation,
    isConnected: !!wallet?.account?.address
  }
}
