import { Address, toNano } from '@ton/core';
import { OnionAuction } from '../build/OnionAuction/OnionAuction_OnionAuction';
import { NetworkProvider } from '@ton/blueprint';

export async function run(provider: NetworkProvider, args: string[]) {
    const ui = provider.ui();

    const address = Address.parse(args.length > 0 ? args[0] : await ui.input('OnionAuction address'));
    const minPurchaseAmount = args.length > 1 ? args[1] : await ui.input('Minimum purchase amount (in TON)');

    if (!(await provider.isContractDeployed(address))) {
        ui.write(`Error: Contract at address ${address} is not deployed!`);
        return;
    }

    const onionAuction = provider.open(OnionAuction.fromAddress(address));

    // Convert the amount to nanotons
    const minPurchaseNano = toNano(minPurchaseAmount);

    console.log(`Setting minimum purchase amount to: ${minPurchaseAmount} TON (${minPurchaseNano.toString()} nanotons)`);

    // Send SetMinPurchase message
    const result = await onionAuction.send(
        provider.sender(),
        {
            value: toNano('0.05'), // Gas fee
        },
        {
            $$type: 'SetMinPurchase',
            min_purchase: minPurchaseNano
        }
    );

    console.log('Transaction sent:', result);
    
    // Wait a bit and then check the new minimum purchase amount
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    try {
        const currentMinPurchase = await onionAuction.getMinPurchase();
        console.log(`Current minimum purchase amount: ${currentMinPurchase.toString()} nanotons (${Number(currentMinPurchase) / 1e9} TON)`);
    } catch (error) {
        console.log('Could not retrieve current minimum purchase amount:', error);
    }
}
